"""
决策树参数对比演示
展示不同参数设置对模型性能的影响
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import accuracy_score, confusion_matrix
import matplotlib
matplotlib.use('Agg')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def test_different_parameters():
    """测试不同参数设置的效果"""
    print("开始参数对比实验...")
    
    # 生成数据
    X, y = make_classification(n_samples=200, n_features=2, n_informative=2, 
                               n_redundant=0, random_state=42)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # 不同的参数配置
    configs = [
        {'max_depth': None, 'min_samples_split': 2, 'name': '默认参数'},
        {'max_depth': 3, 'min_samples_split': 2, 'name': '限制深度=3'},
        {'max_depth': 5, 'min_samples_split': 2, 'name': '限制深度=5'},
        {'max_depth': None, 'min_samples_split': 10, 'name': '最小分割样本=10'},
        {'max_depth': 3, 'min_samples_split': 10, 'name': '深度=3,分割=10'}
    ]
    
    results = []
    
    # 测试每种配置
    for i, config in enumerate(configs):
        print(f"\n测试配置 {i+1}: {config['name']}")
        
        # 训练模型
        model = DecisionTreeClassifier(
            max_depth=config['max_depth'],
            min_samples_split=config['min_samples_split'],
            random_state=42
        )
        model.fit(X_train, y_train)
        
        # 预测和评估
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        # 记录结果
        result = {
            'config': config['name'],
            'accuracy': accuracy,
            'depth': model.get_depth(),
            'leaves': model.get_n_leaves(),
            'model': model
        }
        results.append(result)
        
        print(f"准确率: {accuracy:.4f}")
        print(f"树深度: {model.get_depth()}")
        print(f"叶子节点数: {model.get_n_leaves()}")
    
    # 可视化对比结果
    plt.figure(figsize=(15, 10))
    
    # 子图1: 准确率对比
    plt.subplot(2, 3, 1)
    configs_names = [r['config'] for r in results]
    accuracies = [r['accuracy'] for r in results]
    bars = plt.bar(range(len(configs_names)), accuracies, color='skyblue')
    plt.title('不同参数配置的准确率对比', fontsize=14)
    plt.ylabel('准确率', fontsize=12)
    plt.xticks(range(len(configs_names)), configs_names, rotation=45, ha='right')
    
    # 在柱状图上显示数值
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{acc:.3f}', ha='center', va='bottom')
    
    # 子图2: 树深度对比
    plt.subplot(2, 3, 2)
    depths = [r['depth'] for r in results]
    bars = plt.bar(range(len(configs_names)), depths, color='lightcoral')
    plt.title('决策树深度对比', fontsize=14)
    plt.ylabel('树深度', fontsize=12)
    plt.xticks(range(len(configs_names)), configs_names, rotation=45, ha='right')
    
    for bar, depth in zip(bars, depths):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                str(depth), ha='center', va='bottom')
    
    # 子图3: 叶子节点数对比
    plt.subplot(2, 3, 3)
    leaves = [r['leaves'] for r in results]
    bars = plt.bar(range(len(configs_names)), leaves, color='lightgreen')
    plt.title('叶子节点数对比', fontsize=14)
    plt.ylabel('叶子节点数', fontsize=12)
    plt.xticks(range(len(configs_names)), configs_names, rotation=45, ha='right')
    
    for bar, leaf in zip(bars, leaves):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                str(leaf), ha='center', va='bottom')
    
    # 子图4-6: 前三个配置的决策边界
    for i in range(3):
        plt.subplot(2, 3, 4+i)
        model = results[i]['model']
        
        # 创建网格
        h = 0.02
        x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
        y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                             np.arange(y_min, y_max, h))
        
        # 预测网格点
        Z = model.predict(np.c_[xx.ravel(), yy.ravel()])
        Z = Z.reshape(xx.shape)
        
        # 绘制决策边界
        plt.contourf(xx, yy, Z, alpha=0.4, cmap=plt.cm.RdYlBu)
        
        # 绘制数据点
        colors = ['red', 'blue']
        for j in range(2):
            mask = y == j
            plt.scatter(X[mask, 0], X[mask, 1], c=colors[j], 
                       alpha=0.8, s=30)
        
        plt.title(f'{results[i]["config"]}\n准确率: {results[i]["accuracy"]:.3f}', fontsize=10)
        plt.xlabel('特征 1', fontsize=10)
        plt.ylabel('特征 2', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('parameter_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 打印总结
    print("\n" + "="*60)
    print("参数对比实验总结")
    print("="*60)
    
    best_accuracy = max(accuracies)
    best_config = configs_names[accuracies.index(best_accuracy)]
    
    print(f"最佳准确率: {best_accuracy:.4f} (配置: {best_config})")
    print("\n各配置详细结果:")
    for result in results:
        print(f"- {result['config']}: 准确率={result['accuracy']:.4f}, "
              f"深度={result['depth']}, 叶子节点={result['leaves']}")
    
    print("\n参数影响分析:")
    print("1. 限制树深度可以防止过拟合，但可能降低准确率")
    print("2. 增加最小分割样本数可以简化模型，提高泛化能力")
    print("3. 需要在模型复杂度和性能之间找到平衡")
    print("="*60)
    
    return results

if __name__ == "__main__":
    results = test_different_parameters()
    print("\n参数对比图已保存为 parameter_comparison.png")
