"""
二分类决策树模型实现
按照机器学习文档流程编写的完整代码
包括数据生成、可视化、模型训练、预测和评估
"""

# 1. 导入所需库
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import accuracy_score, confusion_matrix
import seaborn as sns

# 设置中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

class BinaryClassificationModel:
    """二分类决策树模型类"""
    
    def __init__(self, random_state=42):
        """初始化模型"""
        self.random_state = random_state
        self.model = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.y_pred = None
        
    def generate_classification_data(self, n_samples=100, n_features=2):
        """
        2. 数据生成
        生成二分类数据集
        """
        print(f"正在生成 {n_samples} 个样本的二分类数据集...")
        
        # 使用make_classification生成数据
        X, y = make_classification(
            n_samples=n_samples,
            n_features=n_features,
            n_informative=n_features,  # 所有特征都有用
            n_redundant=0,  # 无冗余特征
            n_clusters_per_class=1,  # 每个类别一个簇
            random_state=self.random_state
        )
        
        print(f"数据生成完成！特征维度: {X.shape}, 标签分布: {np.bincount(y)}")
        return X, y
    
    def visualize_data(self, X, y):
        """
        3. 数据可视化
        绘制二维散点图显示数据分布
        """
        print("正在可视化数据分布...")
        
        plt.figure(figsize=(10, 8))
        
        # 使用不同颜色和标记表示不同类别
        colors = ['red', 'blue']
        labels = ['类别 0', '类别 1']
        
        for i in range(2):
            mask = y == i
            plt.scatter(X[mask, 0], X[mask, 1], 
                       c=colors[i], label=labels[i], 
                       alpha=0.7, s=60)
        
        plt.title('生成的二分类数据分布图', fontsize=16, fontweight='bold')
        plt.xlabel('特征 1', fontsize=14)
        plt.ylabel('特征 2', fontsize=14)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
        print("数据可视化完成！")
    
    def split_data(self, X, y, test_size=0.2):
        """
        4. 训练集与测试集划分
        按指定比例划分数据
        """
        print(f"正在划分数据集，测试集比例: {test_size}")
        
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=test_size, random_state=self.random_state
        )
        
        print(f"数据划分完成！")
        print(f"训练集大小: {self.X_train.shape[0]}")
        print(f"测试集大小: {self.X_test.shape[0]}")
        print(f"训练集标签分布: {np.bincount(self.y_train)}")
        print(f"测试集标签分布: {np.bincount(self.y_test)}")
    
    def train_model(self, max_depth=None, min_samples_split=2):
        """
        5. 模型训练
        使用决策树算法训练模型
        """
        print("正在训练决策树模型...")
        
        # 创建决策树分类器
        self.model = DecisionTreeClassifier(
            max_depth=max_depth,
            min_samples_split=min_samples_split,
            random_state=self.random_state
        )
        
        # 训练模型
        self.model.fit(self.X_train, self.y_train)
        
        print("模型训练完成！")
        print(f"决策树深度: {self.model.get_depth()}")
        print(f"叶子节点数: {self.model.get_n_leaves()}")
    
    def make_predictions(self):
        """
        6. 预测
        使用训练好的模型进行预测
        """
        print("正在进行预测...")
        
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用 train_model() 方法")
        
        # 对测试集进行预测
        self.y_pred = self.model.predict(self.X_test)
        
        print("预测完成！")
        return self.y_pred
    
    def visualize_confusion_matrix(self):
        """
        7. 结果可视化 - 混淆矩阵
        """
        print("正在生成混淆矩阵...")
        
        if self.y_pred is None:
            raise ValueError("尚未进行预测，请先调用 make_predictions() 方法")
        
        # 计算混淆矩阵
        cm = confusion_matrix(self.y_test, self.y_pred)
        
        # 绘制混淆矩阵
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['预测类别 0', '预测类别 1'],
                   yticklabels=['实际类别 0', '实际类别 1'])
        
        plt.title('混淆矩阵', fontsize=16, fontweight='bold')
        plt.xlabel('预测标签', fontsize=14)
        plt.ylabel('真实标签', fontsize=14)
        plt.tight_layout()
        plt.show()
        
        print("混淆矩阵可视化完成！")
        return cm
    
    def evaluate_model(self):
        """
        8. 性能评估
        计算并显示模型性能指标
        """
        print("正在评估模型性能...")
        
        if self.y_pred is None:
            raise ValueError("尚未进行预测，请先调用 make_predictions() 方法")
        
        # 计算准确率
        accuracy = accuracy_score(self.y_test, self.y_pred)
        
        # 计算混淆矩阵
        cm = confusion_matrix(self.y_test, self.y_pred)
        
        # 计算其他指标
        tn, fp, fn, tp = cm.ravel()
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        # 显示结果
        print("\n" + "="*50)
        print("模型性能评估结果")
        print("="*50)
        print(f"准确率 (Accuracy): {accuracy:.4f}")
        print(f"精确率 (Precision): {precision:.4f}")
        print(f"召回率 (Recall): {recall:.4f}")
        print(f"F1分数 (F1-Score): {f1_score:.4f}")
        print("="*50)
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'confusion_matrix': cm
        }
    
    def visualize_decision_boundary(self, X, y):
        """
        额外功能：可视化决策边界
        """
        print("正在绘制决策边界...")
        
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用 train_model() 方法")
        
        # 创建网格点
        h = 0.02  # 步长
        x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
        y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                            np.arange(y_min, y_max, h))
        
        # 预测网格点
        Z = self.model.predict(np.c_[xx.ravel(), yy.ravel()])
        Z = Z.reshape(xx.shape)
        
        # 绘制决策边界
        plt.figure(figsize=(12, 8))
        plt.contourf(xx, yy, Z, alpha=0.4, cmap=plt.cm.RdYlBu)
        
        # 绘制数据点
        colors = ['red', 'blue']
        labels = ['类别 0', '类别 1']
        
        for i in range(2):
            mask = y == i
            plt.scatter(X[mask, 0], X[mask, 1], 
                       c=colors[i], label=labels[i], 
                       alpha=0.8, s=60, edgecolors='black')
        
        plt.title('决策树分类器的决策边界', fontsize=16, fontweight='bold')
        plt.xlabel('特征 1', fontsize=14)
        plt.ylabel('特征 2', fontsize=14)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
        print("决策边界可视化完成！")


def main():
    """主函数：执行完整的机器学习流程"""
    print("开始执行二分类决策树模型训练流程")
    print("="*60)
    
    # 创建模型实例
    classifier = BinaryClassificationModel(random_state=42)
    
    # 1. 生成数据
    X, y = classifier.generate_classification_data(n_samples=200, n_features=2)
    
    # 2. 可视化原始数据
    classifier.visualize_data(X, y)
    
    # 3. 划分数据集
    classifier.split_data(X, y, test_size=0.2)
    
    # 4. 训练模型
    classifier.train_model(max_depth=5, min_samples_split=5)
    
    # 5. 进行预测
    classifier.make_predictions()
    
    # 6. 可视化混淆矩阵
    classifier.visualize_confusion_matrix()
    
    # 7. 评估模型性能
    results = classifier.evaluate_model()
    
    # 8. 可视化决策边界
    classifier.visualize_decision_boundary(X, y)
    
    print("\n二分类决策树模型训练流程完成！")
    return classifier, results


if __name__ == "__main__":
    # 运行主程序
    model, evaluation_results = main()
