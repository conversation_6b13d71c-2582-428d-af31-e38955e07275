# 二分类决策树模型实现

本项目按照机器学习文档流程实现了一个完整的二分类决策树模型，包括数据生成、可视化、模型训练、预测和评估。

## 文件说明

- `simple_decision_tree.py` - 简化版实现，严格按照文档流程
- `decision_tree_classifier.py` - 完整版实现，包含类封装和更多功能
- `requirements.txt` - 项目依赖包
- `README.md` - 项目说明文档

## 功能特点

### 1. 数据生成
- 使用 `make_classification` 生成二分类数据集
- 可自定义样本数量和特征数量
- 确保数据的可重现性（random_state=42）

### 2. 数据可视化
- 二维散点图显示数据分布
- 不同颜色表示不同类别
- 支持中文标签显示

### 3. 数据划分
- 按8:2比例划分训练集和测试集
- 显示划分后的数据统计信息

### 4. 模型训练
- 使用决策树分类算法
- 可配置决策树参数
- 显示训练后的模型信息

### 5. 模型预测
- 对测试集进行预测
- 返回预测结果

### 6. 结果可视化
- 混淆矩阵热力图
- 决策边界可视化
- 清晰的图表标注

### 7. 性能评估
- 准确率计算
- 精确率、召回率、F1分数
- 详细的性能报告

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行代码

### 简化版（推荐）
```bash
python simple_decision_tree.py
```

### 完整版
```bash
python decision_tree_classifier.py
```

## 代码流程

1. **导入所需库** - 导入numpy、matplotlib、sklearn等必要库
2. **数据生成** - 使用make_classification生成二分类数据
3. **数据可视化** - 绘制散点图显示数据分布
4. **数据划分** - 划分训练集和测试集
5. **模型训练** - 训练决策树分类器
6. **模型预测** - 对测试集进行预测
7. **结果可视化** - 显示混淆矩阵
8. **性能评估** - 计算并显示各项性能指标

## 输出结果

程序运行后会显示：
- 数据生成信息
- 数据分布散点图
- 训练和测试集统计
- 混淆矩阵热力图
- 决策边界可视化
- 详细的性能评估报告

## 性能指标

- **准确率 (Accuracy)**: 预测正确的样本占总样本的比例
- **精确率 (Precision)**: 预测为正例中实际为正例的比例
- **召回率 (Recall)**: 实际正例中被正确预测的比例
- **F1分数**: 精确率和召回率的调和平均数

## 注意事项

- 确保已安装所有依赖包
- 代码支持中文显示，需要系统安装SimHei字体
- 可以通过修改参数来调整数据集大小和模型参数
- 所有随机种子已固定，确保结果可重现
