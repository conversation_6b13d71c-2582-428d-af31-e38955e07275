"""
简化版二分类决策树模型
严格按照文档流程实现
"""

# 1. 导入所需库
import numpy as np  
import matplotlib.pyplot as plt  
from sklearn.datasets import make_classification  
from sklearn.model_selection import train_test_split  
from sklearn.tree import DecisionTreeClassifier  
from sklearn.metrics import accuracy_score, confusion_matrix

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 2. 数据生成
def generate_data(n=100):
    """生成二分类数据集"""
    # 使用make_classification生成二分类数据集
    X, y = make_classification(n_samples=n, n_features=2, n_informative=2, 
                               n_redundant=0, random_state=42)
    return X, y

# 生成数据
print("正在生成数据...")
X, y = generate_data(150)  # 生成150个样本
print(f"数据生成完成！样本数量: {len(X)}, 特征数量: {X.shape[1]}")
print(f"类别分布: 类别0有{sum(y==0)}个样本, 类别1有{sum(y==1)}个样本")

# 3. 数据可视化
print("\n正在可视化数据...")
plt.figure(figsize=(10, 8))
scatter = plt.scatter(X[:, 0], X[:, 1], c=y, cmap='viridis', s=50, alpha=0.7)
plt.title('生成的二分类数据分布', fontsize=16)
plt.xlabel('特征 1', fontsize=14)
plt.ylabel('特征 2', fontsize=14)
plt.colorbar(scatter, label='类别')
plt.grid(True, alpha=0.3)
plt.show()

# 4. 训练集与测试集划分
print("\n正在划分数据集...")
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
print(f"训练集大小: {len(X_train)}")
print(f"测试集大小: {len(X_test)}")

# 5. 模型训练
print("\n正在训练决策树模型...")
model = DecisionTreeClassifier(random_state=42)    # 创建决策树分类器
model.fit(X_train, y_train)   # 用训练数据进行模型训练
print("模型训练完成！")

# 6. 预测
print("\n正在进行预测...")
y_pred = model.predict(X_test)
print("预测完成！")

# 7. 结果可视化 - 混淆矩阵
print("\n正在生成混淆矩阵...")
cm = confusion_matrix(y_test, y_pred)  # 计算混淆矩阵

plt.figure(figsize=(8, 6))
plt.imshow(cm, cmap='Blues')  # 显示混淆矩阵
plt.title('混淆矩阵', fontsize=16)
plt.xlabel('预测值', fontsize=14)
plt.ylabel('实际值', fontsize=14)

# 在每个格子中显示数值
for i in range(cm.shape[0]):
    for j in range(cm.shape[1]):
        plt.text(j, i, str(cm[i, j]), ha='center', va='center', fontsize=12)

plt.colorbar()  # 显示颜色条
plt.show()

# 8. 性能评估
print("\n正在评估模型性能...")
accuracy = accuracy_score(y_test, y_pred)   # 计算预测准确率
print(f'准确率: {accuracy:.4f}')

# 详细的性能报告
print("\n详细性能报告:")
print("="*40)
print(f"测试样本总数: {len(y_test)}")
print(f"预测正确数量: {sum(y_test == y_pred)}")
print(f"预测错误数量: {sum(y_test != y_pred)}")
print(f"准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

# 混淆矩阵分析
tn, fp, fn, tp = cm.ravel()
print(f"\n混淆矩阵分析:")
print(f"真负例 (TN): {tn}")
print(f"假正例 (FP): {fp}")
print(f"假负例 (FN): {fn}")
print(f"真正例 (TP): {tp}")

# 计算其他指标
if tp + fp > 0:
    precision = tp / (tp + fp)
    print(f"精确率: {precision:.4f}")
else:
    print("精确率: 无法计算 (tp + fp = 0)")

if tp + fn > 0:
    recall = tp / (tp + fn)
    print(f"召回率: {recall:.4f}")
else:
    print("召回率: 无法计算 (tp + fn = 0)")

print("="*40)

# 额外：可视化决策边界
print("\n正在绘制决策边界...")
def plot_decision_boundary():
    """绘制决策边界"""
    plt.figure(figsize=(12, 8))
    
    # 创建网格
    h = 0.02
    x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
    y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                         np.arange(y_min, y_max, h))
    
    # 预测网格点
    Z = model.predict(np.c_[xx.ravel(), yy.ravel()])
    Z = Z.reshape(xx.shape)
    
    # 绘制决策边界
    plt.contourf(xx, yy, Z, alpha=0.4, cmap=plt.cm.RdYlBu)
    
    # 绘制数据点
    colors = ['red', 'blue']
    for i in range(2):
        mask = y == i
        plt.scatter(X[mask, 0], X[mask, 1], c=colors[i], 
                   label=f'类别 {i}', alpha=0.8, s=50)
    
    plt.title('决策树分类器的决策边界', fontsize=16)
    plt.xlabel('特征 1', fontsize=14)
    plt.ylabel('特征 2', fontsize=14)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

plot_decision_boundary()

print("\n程序执行完成！")
print("所有步骤已按照文档流程完成：")
print("1. ✓ 导入所需库")
print("2. ✓ 数据生成")
print("3. ✓ 数据可视化")
print("4. ✓ 训练集与测试集划分")
print("5. ✓ 模型训练")
print("6. ✓ 预测")
print("7. ✓ 结果可视化")
print("8. ✓ 性能评估")
